import React, { useEffect, useState, useRef } from 'react'
import { Table, Button, Spin } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import { css } from 'emotion'
import PageHeader from '../components/pageHeader'
import Search from '../components/search'
import './index.less'

const MeetManage = () => {
  const [loading, setLoading] = useState(false)
  const [searchValues, setSearchValues] = useState({})
  const [dataSource, setDataSource] = useState([])

  const searchRef = useRef(null)
  const columns = [
    { title: '序号', dataIndex: 'index', key: 'index', align: 'center' },
    { title: '会议日期', dataIndex: 'meetingDate', key: 'meetingDate', align: 'center' },
    { title: '会议名称', dataIndex: 'meetingName', key: 'meetingName', align: 'center' },
    { title: '会议状态', dataIndex: 'meetingStatus', key: 'meetingStatus', align: 'center' },
    { title: '会议类型', dataIndex: 'meetingType', key: 'meetingType', align: 'center' },
    { title: '会议室', dataIndex: 'meetingPlace', key: 'meetingPlace', align: 'center' },
    { title: '开始时间', dataIndex: 'startTime', key: 'startTime', align: 'center' },
    { title: '参会领导', dataIndex: 'chld', key: 'chld', align: 'center' },
    { title: '参会经办', dataIndex: 'meetingPerson', key: 'meetingPerson', align: 'center' },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      width: 260,
      render: (text, record) => {
        return (
          <div>
            <Button type="link">议题管理</Button>
            <Button type="link">编辑</Button>
            <Button type="link">删除</Button>
          </div>
        )
      }
    }
  ]
  const getData = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
      setDataSource(Array.from({ length: 34 }).map((item, index) => {
        return {
          index: index + 1,
          meetingDate: '2020-01-01',
          meetingName: '会议名称',
          meetingStatus: '会议状态',
          meetingType: '会议类型',
          meetingPlace: '会议室',
          startTime: '开始时间',
          chld: '参会领导',
          meetingPerson: '参会经办',
          action: '操作',
        }
      }))
    }, 2000)
  }
  const onSubmit = v => {
    console.log(v)
  }

  const onReset = () => {
    setSearchValues({})
  }

  useEffect(() => {
    console.log('=============', searchRef.current)
    getData()
  }, [])
  return (
    <div className={css`
      margin-top: -86px;
      margin-left: -59px;
      margin-right: -59px;
      padding: 20px;
    `}>
      <Spin
        spinning={loading}
        style={{ height: '100vh', overflow: 'hidden', maxHeight: 'initial' }}
      >
        <PageHeader
          title={'会议管理'}
          hasBack={true}
          extra={
            <Button icon={<PlusOutlined/>} type='primary' onClick={() => {}}>
              创建会议
            </Button>
          }
        />
        <div
          className={css`
          overflow: auto;
        `}
        >
          <div className="box meetManage-search-table">
            <Search onSubmit={onSubmit} onReset={onReset} ref={searchRef} />
            <Table
              columns={columns}
              rowKey="formId"
              dataSource={dataSource}
            />
          </div>
        </div>
      </Spin>
    </div>

  )
}

export default MeetManage
