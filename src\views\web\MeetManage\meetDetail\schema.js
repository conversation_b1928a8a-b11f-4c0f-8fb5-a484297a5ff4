import moment from 'moment'

export default () => ({
  type: 'object',
  properties: {
    layout: {
      'x-component': 'mega-layout',
      type: 'object',
      'x-component-props': {
        autoRow: true,
        grid: true,
        columns: 6,
        labelAlign: 'left',
        labelCol: 24,
        full: true,
      },
      properties: {
        meetingName: {
          type: 'string',
          title: '会议名称',
          required: true,
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入',
          },
        },
        mandatary: {
          key: 'mandatary',
          name: 'mandatary',
          title: '经办人',
          required: true,
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入',
          },
          'x-mega-props': {
            span: 2,
          },
        },
        meetingDate: {
          type: 'string',
          title: '会议日期',
          required: true,
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'DatePicker',
          'x-component-props': {
            placeholder: '请选择',
          },
          default: moment().format('YYYY-MM-DD'),
        },
        meetingRoom: {
          type: 'string',
          title: '会议室',
          required: true,
          'x-mega-props': {
            span: 2,
          },
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入',
          },
        },
        meetingLeader: {
          key: 'meetingLeader',
          name: 'meetingLeader',
          title: '参会领导',
          required: true,
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请选择',
          },
          'x-mega-props': {
            span: 2,
          },
        },
        meetingType: {
          key: 'meetingType',
          name: 'meetingType',
          title: '会议类型',
          required: true,
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入',
          },
          'x-mega-props': {
            span: 2,
          },
        },
      },
    },
    agendaList: {
      type: 'array',
      'x-component': 'AgendaArrayField',
      'x-component-props': {
        title: '议题列表',
      },
      items: {
        type: 'object',
        properties: {
          reporter: {
            type: 'string',
            title: '汇报人',
            required: true,
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入',
            },
          },
          startTime: {
            type: 'string',
            title: '开始时间',
            required: true,
            'x-component': 'TimePicker',
            'x-component-props': {
              placeholder: '请选择',
              format: 'HH:mm',
            },
          },
          duration: {
            type: 'number',
            title: '汇报时长',
            required: true,
            'x-component': 'InputNumber',
            'x-component-props': {
              placeholder: '请输入',
              min: 1,
              max: 300,
              addonAfter: '分钟',
            },
          },
          agendaName: {
            type: 'string',
            title: '议题名称',
            required: true,
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入',
            },
          },
          reportUnit: {
            type: 'string',
            title: '汇报单位',
            required: true,
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入',
            },
          },
          attendeeContact: {
            type: 'string',
            title: '列席联系人',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入',
            },
          },
          attendeeUnit: {
            type: 'string',
            title: '列席单位',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入',
            },
          },
        },
      },
    },
  },
})
