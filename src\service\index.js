import _ from 'lodash'
import request from './request'
import API from './api'

const CHECK_FUNC = res => res.retcode === 0 || res.success === true
export default {
  /**
   * 获取登录用户所在的集团信息，
   * @param param
   * @returns {Promise.<string>|*}
   */
  getMyself(param) {
    return request.get(API.GET_MYSELF, param).then(res => res)
  },
  queryOrgLinkPath(param) {
    return request.get(API.QUERY_ORG_LINK_PATH, param).then(res => res)
  },
  getOrgLinkPathForDebug(param) {
    return request.get(API.GET_ORG_LINK_PATH_FOR_DEBUG, param).then(res => res)
  },
  deleteForm(param) {
    return request.get(API.DELETE_FORM, param).then(res => res)
  },
  saveForm(param) {
    return request.post(API.SAVE_FORM, param).then(res => res)
  },
  upDateForm(param) {
    return request.post(API.UPDATE_FORM, param).then(res => res)
  },
  // submitForm(param) {
  //    return request.post(API.SUBMIT_FORM, param).then((res) => res)
  // },
  getSigner(param) {
    return request.get(API.GET_SEND_SIGNER, param).then(res => res)
  },
  getDocumentWatermark(param) {
    return request.post('/access/DocumentCommon/getDocumentWatermark', param).then(res => res)
  },
  getPersonalInfo(param) {
    return request.get(API.GET_PERSONAL_INFO, param).then(res => res)
  },
  getFormData(param) {
    return request.get(API.GET_FORM_DATA, param).then(res => res)
  },
  getDocumentBodyFileList(param) {
    return request.post(API.GET_TEMPLATE, param).then(res => res)
  },
  // getNodeList(param) {
  //    return request.post(API.GET_NODE_LIST, param).then(res=>res)
  // },
  postFeedback(param) {
    return request.post(API.ADD_FEEDBACK, param).then(res => res)
  },
  getFeedbackList(param) {
    return request.get(API.GET_FEEDBACK_LIST, param).then(res => res)
  },
  createFeedback(param) {
    return request.post(API.CREATE_FEEDBACK, param).then(res => res)
  },
  getPdfUrl(param) {
    return request.post(API.CHANGE_TOPDF, param).then(res => res)
  },
  getNumberData(param) {
    return request.post(API.GETLIST_DATA, param).then(res => res)
  },
  getAutoNumber(param) {
    return request.post(API.AUTO_NUMBER, param).then(res => res)
  },
  getHistory(param) {
    return request.post(API.GET_HISTORY, param).then(res => res)
  },
  isShowFeedback(param) {
    return request.post(API.IS_SHOW_FEEDBACK, param).then(res => res)
  },
  getUserLabel(param) {
    return request.get(API.USER_LABEL, param).then(res => res)
  },
  getNumberPrefix(param) {
    return request.post(API.GET_NUMBER_PREFIX, param).then(res => res)
  },
  getDispatchInfo(param) {
    return request.post(API.GET_DISPATCH_INFO, param).then(res => res)
  },
  queryDeptAuthGrantInfo(param) {
    return request.post(API.QUERY_DEPT_AUTH_GRANT_INFO, param).then(res => res.data)
  },
  getStartNode(param) {
    return request
      .post(param.procKey ? API.GET_START_NEXT_NODE : API.GET_NEXT_NODE, param)
      .then(res => res)
  },
  getStartNextCopyNode(param) {
    return request.post(API.GET_START_NEXT_COPY_NODE, param).then(res => res)
  },
  completeTask(param) {
    return request.post(API.COMPLETE_TASK, param).then(res => res)
  },
  startTask(param) {
    return request.post(API.START_TASK, param).then(res => res)
  },
  getFlowID(param) {
    return request.post(API.GET_FLOWID, param).then(res => res)
  },
  saveDraft(param) {
    return request.post(API.SAVE_DRAFT_URL, param).then(res => res)
  },
  deleteDraft(param) {
    return request.post(API.REVOKE_DRAFT_URL, param).then(res => res)
  },
  getCurrentUserOrgType(param) {
    return request.get(API.GET_ORGTYPE, param).then(res => res)
  },
  /**
   * 根据用户等级编码获取用户数据列表
   * @param {Object} param
   * @param {String} param.lnCode - 等级编码
   */
  getLevelCodeUserList(param) {
    return request.post(API.GET_LEVELCODELIST, param).then(res => _.get(res, 'data'))
  },
  sendDocument(param) {
    return request.post(API.SEND_DOCUMENT, param).then(res => res)
  },
  /**
   * 获取密级分发人员等级编码 市/区公司 当前用户是否是普通员工
   * @param {Object} param
   * @param {Number} param.orgId 组织id
   * @param {Number} param.deptId 部门id
   * @param {Number} param.userId 用户id
   * @returns  type类型 1是区公司其他是市公司
   */
  getUserLevelCode(param) {
    return request.post(API.GETUSERLEVEL_CODE, param).then(res => _.get(res, 'data'))
  },
  /**
   * 创建规章制度公文
   * @param {*} param
   * @returns
   */
  createDocumentByBusiness(param) {
    return request.post(API.CREATE_BUSSINESSCODE, param).then(res => _.get(res, 'data'))
  },
  /**
   * 查询规章制度公文是否已存在
   * @param {*} param
   * @returns
   */
  getDocumentIdByBusiness(param) {
    return request.post(API.GET_BUSSINESSCODE, param).then(res => _.get(res, 'data'))
  },
  getDepartmentLeaderList(param) {
    return request.post(API.GET_DEPARTMENTLIST, param).then(res => _.get(res, 'data'))
  },
  getDocumentUsefulExpression(param) {
    return request
      .get(API.GET_USEFULEXPRESSION, { ...param, pageSize: 10, pageIndex: 1 })
      .then(res => _.get(res, 'data'))
  },
  getIsDistribute(param) {
    return request.post(API.GET_DISTRIBUTE, param).then(res => res)
  },

  // 用车
  getOrgType(param) {
    return request.get(API.ORG_TYPE, param).then(res => res)
  },
  getGridList(param) {
    return request.get(API.GRID_LIST, param).then(res => res)
  },
  applyCreate(param) {
    return request.post(API.APPLY_CREATE, param).then(res => res)
  },
  applyEdit(param) {
    return request.post(API.APPLY_EDIT, param).then(res => res)
  },
  getApplyDetail(param) {
    return request.get(API.APPLY_DETAIL, param).then(res => res)
  },
  applyRecordEdit(param) {
    return request
      .post(API.APPLY_RECORD_DETAIL, param)
      .then(res => res)
      .catch(err => err)
  },
  carInfoAdd(param) {
    return request.post(API.CAR_INFO_ADD, param).then(res => res)
  },
  driverInfoAdd(param) {
    return request.post(API.DRIVER_INFO_ADD, param).then(res => res)
  },
  getCarInfoList(param) {
    return request.get(API.CAR_INFO_LIST, param).then(res => res)
  },
  getDriverInfoList(param) {
    return request.get(API.DRIVER_INFO_LIST, param).then(res => res)
  },
  getInfo(param) {
    return request.get(API.GET_INFO, param).then(res => res)
  },
  getPreviewUrl(param) {
    return request.get(API.GET_PREVIEW_URL, param).then(res => res)
  },
  getDepts(param) {
    return request.get(API.GET_DEPTS, param).then(res => res)
  },
  getUserLabelList(param) {
    return request.get(API.GET_USER_LABELS, param).then(res => res)
  },
  getWpsParagraph(param) {
    return request.post(API.GET_WPS_PARAGRAPH, param).then(res => res)
  },
  saveParagraphDetail(param) {
    return request.post(API.SAVE_PARAGRAPHDETAIL, param).then(res => res)
  },
  getParagraphDetail(param) {
    return request.get(API.GET_PARAGRAPHDETAIL, param).then(res => res)
  },
  getParagraphContent(param) {
    return request.get(API.GET_PARAGRAPHCONTENT, param).then(res => res)
  },
  uploadFile(param) {
    return request
      .post(API.UPLOADFILE, param)
      .then(res => res)
      .catch(err => err)
  },
  //  oa权限保存临时数据
  oaPermissionSaveftDraft(param) {
    return request.post(API.OA_PERMISSION_SAVE_DRAFT, param).then(res => res)
  },
  // oa权限获取临时数据
  oaPermissionGetDraft(param) {
    return request.get(API.OA_PERMISSION_GET_DRAFT, param).then(res => res)
  },
  // 协调函获取文件编码
  getSerialNumber(param) {
    return request
      .get(API.GET_SERIALNUMBER_URL, param)
      .then(res => res)
      .catch(err => err)
  },
  selectProcinstByNameAndProcKey(param) {
    return request.post(API.SELECT_PROCINS_BYNAME_AND_PROCKEY, param).then(res => res)
  },
  // 判断流程中表单是否可编辑
  getEditableForm(param) {
    return request
      .get(API.GET_EDITABLE_FORM, param)
      .then(res => res)
      .catch(err => err)
  },

  getOrgIdList(param) {
    return request
      .post(API.GET_ORGID_LIST, param)
      .then(res => res)
      .catch(err => err)
  },

  downLoad(param) {
    return request.post(API.DOWN_FILE, param).then(res => res)
  },
  saveWpsEditUrl: param => {
    return request.post(API.SAVE_URL, param).then(json => json.data)
  },
  getWpsEditUrl: param => {
    return request.post(API.GET_URL, param).then(json => json.data)
  },
  paragraphClear: param => {
    return request.get(API.PARAGRAH_CLEAR, param).then(json => json.data)
  },
  batchGetCustomerFormPrincipal: param => {
    return request.post(API.BATCH_GET_CUSTOMER_FORM_PRINCIPAL, param).then(json => json)
  },
  reportSave: param => {
    return request.post(API.REPORT_SAVE, param).then(json => json)
  },
  getReportSerialTemplate: param => {
    return request.get(API.GET_REPORT_SERIAL_TEMPLATE, param).then(json => json.data)
  },
  serialMake: param => {
    return request.get(API.REPORT_SERIAL_MAKE, param).then(json => json.data)
  },
  recordSave: param => {
    return request.post(API.REPORT_RECORD_SAVE, param).then(json => json)
  },
  getReportQuertList: param => {
    return request.get(API.REPORT_QUERY_LIST, param).then(json => json)
  },
  getReportDetail: param => {
    return request.get(API.GET_REPORT_DETAIL, param).then(json => json)
  },
  queryCollectDate: param => {
    return request.get(API.QUERY_COLLECT_DATE, param).then(json => json)
  },
  getUserTask: param => {
    return request.get(API.GET_UESRTASK, param).then(json => json)
  },

  getInterviewDetail: param => {
    return request.get(API.GET_UESRTASK, param).then(json => json)
  },

  saveInterview: param => {
    return request.post(API.SAVE_INTERVIEW, param).then(json => json.data)
  },

  updateSubForm: param => {
    return request.post(API.UPDATE_SUB_APPOINTMENT, param).then(json => json)
  },

  saveAppointment: param => {
    return request.post(API.SAVE_APPOINTMENT, param).then(json => json)
  },
  getAppointmentInfo: param => {
    return request.get(API.GET_APPOINTMENT_INFO, param).then(json => json)
  },
  getOrgByLnCodes: param => {
    return request.post(API.GET_ORG_BY_LNCODES, param).then(json => json.data || [])
  },
  getDeptByLnCodesAndOrg: param => {
    return request.post(API.GET_DEPT_BY_LNCODES_AND_ORG, param).then(json => json.data || [])
  },
  getUsersByLnCodesAndDept: param => {
    return request.post(API.GET_USERS_BY_LNCODES_AND_DEPT, param).then(json => json.data || [])
  },
  getUserDefLabels: param => {
    return request.post(API.GET_USER_DEF_LABELS, param).then(json => json)
  },
  getUsersWithLnGroupsByLnCodes: param => {
    return request.post(API.GET_USERS_WITH_LNGROUPS_BY_LNCODES, param).then(json => json.data || [])
  },
  getCurrentOrgType: param => {
    return request.get(API.GET_CURRENT_ORGTYPE, param).then(json => json.data)
  },
  getAppointmentMemberInfo: param => {
    return request.get(API.GET_APPOINTMENT_MEMBER_INFO, param).then(json => json)
  },
  getNextNodes: param => {
    return request.get(API.NEXT_ACTINST, param).then(json => json)
  },
  completeManualTask: param => {
    return request.post(API.COMPLETE_MANUAL_TASK, param).then(json => json)
  },
  getLabelNameByUids: param => {
    return request.post(API.GET_LABEL_NAME_BY_UIDS, param).then(json => json.data || [])
  },
  getOrgAllUserByLnCodes: param => {
    return request.post(API.GET_ORG_ALL_USER_BY_LNCODES, param).then(json => json.data || [])
  },
  deleteReport: param => {
    return request.post(API.DETELE_REPORT, param).then(json => json)
  },
  getTips(param) {
    return request.post(API.GET_TIPS, param).then(json => json) // 获取管控提醒
  },
  // 是否纳入管控
  saveControl(param) {
    return request.post(API.SAVE_CONTROL, param).then(json => json)
  },

  getOrgByResearch: param => {
    return request.get(API.GET_ORG_BY_RESEARCH, param).then(json => json.data)
  },
  saveFilePermission: param => {
    return request.post(API.SAVE_FILE_PERMISSION, param).then(json => json.data)
  },
  getFilePermission: param => {
    return request.get(API.GET_FILE_PERMISSION, param).then(json => json.data)
  },
  getReadFile: param => {
    return request.get(API.GET_READ_FILE, param).then(json => json.data)
  },
  // 获取同步预约会议室列表
  getRoom: param => {
    return request.get(API.GET_ROOM, param).then(json => json)
  },
  // 此接口可以校验新旧数据的判别
  checkNewData: param => {
    return request.get(API.CHECK_NEW_DATA, param).then(json => json)
  },
  // report和会议室的关联信息
  connection: param => {
    return request.get(API.CONNECTION, param).then(json => json)
  },
  // 获取已预约会议列表
  getMeeting: param => {
    return request.get(API.GET_MEETING, param).then(json => json)
  },
  // 检查已预约会议是否已经被关联
  checkMeeting: param => {
    return request.get(API.CHECK_MEETING, param).then(json => json)
  },
  // 校验同步预约会议室时间是否冲突已被预订
  checkRoom: param => {
    return request.get(API.CHECK_ROOM, param).then(json => json)
  },
  // 添加或者修改预约会议选项
  addMeeting: param => {
    return request.post(API.ADD_MEETING, param).then(json => json)
  },
  // 报名人员下载数据
  export: param => {
    return request.get(API.EXPORT, param).then(json => json)
  },
  // 签到列表
  signList: param => {
    return request.get(API.SIGN_LIST, param).then(json => json)
  },
  // 签到下载
  signExport: param => {
    return request.get(API.SIGN_EXPORT, param).then(json => json)
  },
  // 会务短信-查询会务短信发送记录
  listSendMessageRecord: param => {
    return request.get(API.LIST_SEND, param).then(json => json)
  },
  // 会务短信-会务短信发送接口
  sendMessage: param => {
    return request.post(API.SEND_MESSAGE, param).then(json => json)
  },
  getListUserChangeMeeting: param => {
    return request.get(API.LIST_USER_CHANGE_MEETING, param).then(json => json)
  },

  // 此接口可以校验新旧数据的判别

  // 通过传参 获取人员的组织路径
  // eslint-disable-next-line no-dupe-keys
  getOrgLinkPathForDebug: param => {
    return request.get(API.GET_ORGLINKPATH, param).then(json => json)
  },

  // 查询反馈日期
  getFeedbackDay: param => {
    return request.get(API.GET_FEEDBACK_DAY, param).then(json => json)
  },
  // 新增/编辑反馈
  saveFeedback: param => {
    return request.post(API.SAVEFEEDBACK, param).then(json => json)
  },
  // 查询反馈详情
  getFeedbackDetail: param => {
    return request.get(API.GET_FEEDBACK_DETAIL, param).then(json => json)
  },
  // 查询反馈内容
  getWorkFeedbackList: param => {
    return request.get(API.FEEDBACK_LIST, param).then(json => json)
  },
  // 查询反馈内容
  exportFeedback: param => {
    return request.get(API.EXPORT_FEEDBACK, param).then(json => json)
  },

  // 获取上级orgId
  getOneById: param => {
    return request.get(API.GET_ONE_BY_ID, param).then(json => json)
  },

  // 获取职位信息
  getOneUser: param => {
    return request.get(API.GET_ONE_USER, param).then(json => json.data)
  },

  // 批量获取职位信息
  getMultiUser: param => {
    return request.post(API.GET_MULTI_USER, param).then(json => json.data)
  },

  // 会议签到消息内容详细查询
  meetingSignDetail: param => {
    return request.get(API.GET_MEETING_SIGN_DETAIL, param).then(json => json)
  },

  // 根据表单数据查询流程状态
  getProcInstInfoOfForm: param => {
    return request.post(API.GET_PROC_INS_INFO_OF_FORM, param).then(json => json.data)
  },

  // 检查用户是否是【党委书记】
  checkRole: param => {
    return request.post(API.CHECK_ROLE, param).then(json => json.data)
  },

  //会议通知中获取会议计划
  getMeetingPlan: param => {
    return request.get(API.GET_MEETING_PLAN, param).then(json => json.data) 
  },

  // 会议管理
  // 获取会议列表
  getMeetingList: param => {
    return request.get(API.GET_MEETING_LIST, param).then(json => json.data)
  },
  // 新增会议
  addMeeting: param => {
    return request.post(API.SAVE_MEETING, param).then(json => json)
  },
  // 修改议题状态
  editMeeting: param => {
    return request.post(API.UPDATE_TOPICS_STATUS, param).then(json => json)
  },
  // 会议详情
  getMeetingDetail: param => {
    return request.get(API.GET_MEETING_DETAIL, param).then(json => json.data)
  },
  // 删除会议
  deleteMeeting: param => {
    return request.post(API.DELETE_MEETING, param).then(json => json)
  },
}
