import React, { useEffect, useState, useMemo } from 'react'
import { Spin, message, TimePicker, InputNumber } from 'antd'
import { Input, Select, Radio, FormItemGrid, DatePicker, FormCard, FormBlock, FormMegaLayout } from '@formily/antd-components'
import { SchemaMarkupForm, createAsyncFormActions } from '@formily/antd'
import SelectDept from 'ROOT/components/Formily/deptSelect'
import SelectMember from 'ROOT/components/Formily/userSelect'
import EditableInput from 'ROOT/components/Formily/Editable'
import { css } from 'emotion'
// import PageHeader from '../components/pageHeader'
import schema from './schema'
import AgendaArrayField from './components/AgendaSection'
import FormActions from './components/FormActions'
import './index.css'
const MeetManage = () => {
  const [loading, setLoading] = useState(false)
  const [editable, setEditable] = useState(true);
  const [initValue, setInitValue] = useState({});
  const actions = useMemo(() => createAsyncFormActions(), []);
  const uesEffects = () => {

  }
  //  数据回填
  useEffect(() => {
    const fetchData = async () => {
      const data = {
        meetingDate: '2020-01-01',
        meetingName: '会议名称',
        meetingStatus: '会议状态',
        meetingType: '会议类型',
        meetingPlace: '会议室',
        startTime: '开始时间',
        chld: '参会领导',
        meetingPerson: '参会经办',
      }
      // setInitValue(data)
      // actions.setFormState(state => {
      //   state.values = data
      // })
    }
    fetchData()
  }, [])
  const expressionScope = {

  }

  // 标记自定义组件为字段组件
  AgendaArrayField.isFieldComponent = true

  const components = {
    Input,
    Select,
    DatePicker,
    FormCard,
    SelectDept,
    SelectMember,
    EditableInput,
    AgendaArrayField,
    TimePicker,
    InputNumber,
    FormBlock,
    FormMegaLayout,
    'mega-layout': FormMegaLayout,
  }

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      setLoading(true)

      // 验证表单
      await actions.validate()

      // 获取表单数据
      const formState = await actions.getFormState()
      const values = formState.values
      debugger
      console.log('表单数据:', values)

      // 额外验证议题数据
      if (values.agendaList && values.agendaList.length > 0) {
        const invalidAgendas = []
        values.agendaList.forEach((agenda, index) => {
          const errors = []
          if (!agenda.reporter) errors.push('汇报人')
          if (!agenda.startTime) errors.push('开始时间')
          if (!agenda.duration) errors.push('汇报时长')
          if (!agenda.agendaName) errors.push('议题名称')
          if (!agenda.reportUnit) errors.push('汇报单位')

          if (errors.length > 0) {
            invalidAgendas.push(`议题${index + 1}: ${errors.join('、')}不能为空`)
          }
        })

        if (invalidAgendas.length > 0) {
          message.error(`请完善以下信息：\n${invalidAgendas.join('\n')}`)
          return
        }
      }

      // 这里可以调用API保存数据
      // await api.saveMeetingData(values)

      message.success('保存成功')
    } catch (error) {
      console.error('提交失败:', error)
      if (error.name === 'ValidateError') {
        console.log('验证错误详情:', error)
        message.error('请检查表单填写是否完整')
      } else {
        message.error('保存失败')
      }
    } finally {
      setLoading(false)
    }
  }

  // 处理取消
  const handleCancel = () => {
    // 可以添加确认对话框
    window.history.back()
  }
  return (
    <div className="meet-manage-container">
      <Spin
        spinning={loading}
        style={{ height: '100vh', overflow: 'hidden', maxHeight: 'initial' }}
      >
        {/* <PageHeader
          title={'创建会议'}
          hasBack={true}
        /> */}
        <div
          className={css`
          overflow: auto;
          height: 100%;
        `}
        >
          <div className="meet-manage-form" style={{ paddingBottom: '80px' }}>
            <div className={css`
                font-size: 16px;
                font-weight: 500;
                color: #262626;
                margin-bottom: 16px;
              `}>会议基本信息</div>
            <SchemaMarkupForm
              schema={schema()}
              components={components}
              actions={actions}
              effects={() => {
                uesEffects()
              }}
              initialValues={initValue}
              expressionScope={{ ...expressionScope }}
              previewPlaceholder='-'
              editable={editable}
            >
            </SchemaMarkupForm>
          </div>

          <FormActions
            onCancel={handleCancel}
            onSubmit={handleSubmit}
            loading={loading}
          />
        </div>
      </Spin>
    </div>
  )
}

export default MeetManage
