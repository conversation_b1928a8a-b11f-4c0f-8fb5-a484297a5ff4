import React from 'react'
import { Button, Space, Input, TimePicker, DatePicker, message, InputNumber, Popconfirm, Modal } from 'antd'
import { DownloadOutlined, UploadOutlined, PlusOutlined, DeleteOutlined, MenuOutlined } from '@ant-design/icons'
import { sortableContainer, sortableElement, sortableHandle } from 'react-sortable-hoc'
import { arrayMoveImmutable } from 'array-move'
import { css } from 'emotion'
import moment from 'moment'

// 拖拽手柄
const DragHandle = sortableHandle(() => (
  <div className={css`
    display: flex;
    align-items: center;
    cursor: grab;
  `}>
    <MenuOutlined
      style={{
        cursor: 'grab',
        color: '#5C626B',
        fontSize: '12px',
        marginRight: '8px',
      }}
    />
    <span style={{ color: '#5C626B' }}>拖动</span>
  </div>
))

// 可排序的议题项
const SortableItem = sortableElement(({ children }) => children)

// 可排序的容器
const SortableContainer = sortableContainer(({ children }) => children)

const AgendaArrayField = (itemProps) => {
  console.log('AgendaArrayField props:', itemProps)

  // 尝试从不同的地方获取参数
  const value = itemProps.value || []
  const mutators = itemProps.mutators || itemProps.onChange
  const editable = itemProps.editable !== false
  const title = itemProps.title || '议题列表'

  console.log('Extracted values:', { value, mutators, editable, title })

  // 计算下一个议题的开始时间
  const calculateNextStartTime = (agendaList, currentIndex) => {
    if (currentIndex === 0) {
      return '2025-06-03 09:00:00' // 默认第一个议题从9点开始
    }

    const prevAgenda = agendaList[currentIndex - 1]
    if (prevAgenda && prevAgenda.startTime && prevAgenda.duration) {
      const startMoment = moment(prevAgenda.startTime, 'YYYY-MM-DD HH:mm:ss')
      const nextStartTime = startMoment.add(prevAgenda.duration, 'minutes')
      return nextStartTime.format('YYYY-MM-DD HH:mm:ss')
    }

    return '2025-06-03 09:00:00'
  }

  // 更新后续议题的开始时间
  const updateSubsequentStartTimes = (agendaList, fromIndex) => {
    const updatedList = [...agendaList]

    for (let i = fromIndex + 1; i < updatedList.length; i++) {
      const prevAgenda = updatedList[i - 1]
      if (prevAgenda && prevAgenda.startTime && prevAgenda.duration) {
        const startMoment = moment(prevAgenda.startTime, 'HH:mm')
        const nextStartTime = startMoment.add(prevAgenda.duration, 'minutes')
        updatedList[i] = {
          ...updatedList[i],
          startTime: nextStartTime.format('HH:mm')
        }
      }
    }

    return updatedList
  }

  // 处理新增议题
  const handleAdd = () => {
    const nextStartTime = calculateNextStartTime(value, value.length)
    const newAgenda = {
      reporter: '',
      startTime: nextStartTime,
      duration: 30, // 默认30分钟
      agendaName: '',
      reportUnit: '',
      attendeeContact: '',
      attendeeUnit: '',
    }

    // 使用push方法添加新项
    if (mutators.push) {
      mutators.push(newAgenda)
    } else if (mutators.change) {
      const newData = [...value, newAgenda]
      mutators.change(newData)
    }
  }

  // 移除编辑相关的处理函数，因为现在默认全部展开

  // 处理删除议题
  const handleDelete = (index) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个议题吗？',
      onOk: () => {
        if (!mutators) {
          message.error('无法删除议题')
          return
        }

        // 使用remove方法删除项
        if (mutators.remove) {
          mutators.remove(index)
        } else if (mutators.change) {
          const newData = [...value]
          newData.splice(index, 1)
          // 重新计算后续议题的开始时间
          const updatedData = updateSubsequentStartTimes(newData, Math.max(0, index - 1))
          mutators.change(updatedData)
        } else {
          message.error('无法删除议题，mutators方法不可用')
          return
        }

        message.success('删除成功')
      },
    })
  }

  // 处理字段更新
  const handleFieldChange = (index, field, newValue) => {
    console.log('handleFieldChange called:', { index, field, newValue, mutators })

    if (!mutators) {
      console.error('mutators not available for field change')
      return
    }

    const newData = [...value]
    newData[index] = {
      ...newData[index],
      [field]: newValue
    }

    // 如果修改了时间或时长，需要更新后续议题的开始时间
    if (field === 'startTime' || field === 'duration') {
      const updatedData = updateSubsequentStartTimes(newData, index)
      if (mutators.change) {
        mutators.change(updatedData)
      }
    } else {
      if (mutators.change) {
        mutators.change(newData)
      }
    }
  }

  // 处理拖拽排序
  const onSortEnd = ({ oldIndex, newIndex }) => {
    if (oldIndex !== newIndex && mutators && mutators.change) {
      const newData = arrayMoveImmutable(value, oldIndex, newIndex)
      // 重新计算所有议题的开始时间
      const updatedData = updateSubsequentStartTimes(newData, 0)
      mutators.change(updatedData)
    }
  }

  // 处理下载模板
  const handleDownloadTemplate = () => {
    message.info('下载模板功能待实现')
  }

  // 处理批量导入
  const handleBatchImport = () => {
    message.info('批量导入功能待实现')
  }

  // 渲染单个议题项
  const renderAgendaItem = (item, index) => (
    <SortableItem key={index} index={index}>
      <div className={css`
        background-color: #F7F8F9;
        border-radius: 6px;
        margin-bottom: 16px;
        position: relative;
      `}>
        {/* 右上角操作区 */}
        {editable && (
          <div className={css`
            position: absolute;
            top: 16px;
            right: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            z-index: 10;
          `}>
            <Button
              type="link"
              size="small"
              danger
              onClick={() => handleDelete(index)}
              style={{
                color: '#5C626B',
              }}
            >
              <DeleteOutlined style={{ color: '#5C626B' }} />
              <span style={{ color: '#5C626B' }}>删除</span>
            </Button>
            <DragHandle />
          </div>
        )}
        <div className={css`
          font-size: 16px;
          font-weight: 500;
          color: #262626;
          padding: 16px;
          padding-bottom: 0;
        `}>议题{index + 1}</div>
        {/* 第一行：基本信息 */}
        <div className={css`
          padding: 16px 16px 12px 16px;
          display: grid;
          grid-template-columns: 1fr 1fr 1fr;
          gap: 16px;
        `}>
          <div>
            <div className={css`
              font-size: 14px;
              color: #666;
              margin-bottom: 4px;
            `}>
              汇报人 <span style={{ color: '#ff4d4f' }}>*</span>
            </div>
            <Input
              value={item.reporter}
              placeholder="请输入汇报人"
              onChange={(e) => handleFieldChange(index, 'reporter', e.target.value)}
              disabled={!editable}
            />
          </div>

          <div>
            <div className={css`
              font-size: 14px;
              color: #666;
              margin-bottom: 4px;
            `}>
              开始时间
            </div>
            <DatePicker
              showTime
              value={moment(item.startTime, 'YYYY-MM-DD HH:mm:ss')}
              format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择开始时间"
              onChange={(time) => handleFieldChange(index, 'startTime', time ? time.format('YYYY-MM-DD HH:mm:ss') : '')}
              disabled={!editable}
              style={{ width: '100%' }}
            />
            {/* <Input
              value={item.startTime}
              placeholder="请选择开始时间"
              onChange={(e) => handleFieldChange(index, 'startTime', e.target.value)}
              disabled={!editable}
            /> */}
          </div>
          <div>
            <div className={css`
              font-size: 14px;
              color: #666;
              margin-bottom: 4px;
            `}>
              汇报时长
            </div>
            <Input
              value={item.duration}
              placeholder="请输入汇报时长"
              onChange={(e) => handleFieldChange(index, 'duration', e.target.value)}
              disabled={!editable}
            />
          </div>
        </div>
        {/* 第二行：议题名称（单独一行） */}
        <div className={css`
          padding: 0 16px 12px 16px;
        `}>
          <div className={css`
            font-size: 14px;
            color: #666;
            margin-bottom: 4px;
          `}>
            议题名称 <span style={{ color: '#ff4d4f' }}>*</span>
          </div>
          <Input
            value={item.agendaName}
            placeholder="请输入议题名称"
            onChange={(e) => handleFieldChange(index, 'agendaName', e.target.value)}
            disabled={!editable}
          />
        </div>

        <div className={css`
          padding: 0 16px 12px 16px;
        `}>
          <div>
            <div className={css`
              font-size: 14px;
              color: #666;
              margin-bottom: 4px;
            `}>
              汇报单位 <span style={{ color: '#ff4d4f' }}>*</span>
            </div>
            <Input
              value={item.reportUnit}
              placeholder="请输入汇报单位"
              onChange={(e) => handleFieldChange(index, 'reportUnit', e.target.value)}
              disabled={!editable}
            />
          </div>

        </div>
        <div className={css`
          padding: 0 16px 12px 16px;
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 16px;
        `}>

          <div>
            <div className={css`
              font-size: 14px;
              color: #666;
              margin-bottom: 4px;
            `}>
              列席联系人
            </div>
            <Input
              value={item.attendeeContact}
              placeholder="请输入列席联系人"
              onChange={(e) => handleFieldChange(index, 'attendeeContact', e.target.value)}
              disabled={!editable}
            />
          </div>
          <div>
            <div className={css`
            font-size: 14px;
            color: #666;
            margin-bottom: 4px;
          `}>
              列席单位
            </div>
            <Input
              value={item.attendeeUnit}
              placeholder="请输入列席单位"
              onChange={(e) => handleFieldChange(index, 'attendeeUnit', e.target.value)}
              disabled={!editable}
            />
          </div>
        </div>
      </div>
    </SortableItem>
  )

  return (
    <div className={css`
      margin-top: 24px;
    `}>
      <div className={css`
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
      `}>
        <h3 className={css`
          margin: 0;
          font-size: 16px;
          font-weight: 500;
        `}>
          {title}
        </h3>
        {editable && (
          <Space>
            {/* <Button
              icon={<DownloadOutlined />}
              onClick={handleDownloadTemplate}
            >
              下载模板
            </Button>
            <Button
              icon={<UploadOutlined />}
              onClick={handleBatchImport}
            >
              批量导入
            </Button> */}
            <Button
              // type="primary"
              icon={<PlusOutlined style={{ color: '#5C626B' }} />}
              onClick={handleAdd}
            >
              新增议题
            </Button>
          </Space>
        )}
      </div>

      <SortableContainer onSortEnd={onSortEnd} useDragHandle>
        <div>
          {value.length === 0 ? (
            <div className={css`
              padding: 40px;
              text-align: center;
              color: #999;
              background: #fafafa;
              border: 1px solid #f0f0f0;
              border-radius: 6px;
            `}>
              暂无议题，点击"新增议题"添加
            </div>
          ) : (
            value.map((item, index) => renderAgendaItem(item, index))
          )}
        </div>
      </SortableContainer>
    </div>
  )
}

// 展开的编辑表单
const AgendaExpandedForm = ({ item, index, visible, onFieldChange, onSave, onCancel }) => {
  if (!visible) return null

  return (
    <div className={css`
      padding: 16px;
      background: #f8f9fa;
      border-top: 1px solid #e8e8e8;
    `}>
      <div className={css`
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-bottom: 16px;
      `}>
        <div>
          <div className={css`
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
          `}>
            汇报单位 <span style={{ color: '#ff4d4f' }}>*</span>
          </div>
          <Input
            value={item.reportUnit}
            placeholder="请输入汇报单位"
            onChange={(e) => onFieldChange(index, 'reportUnit', e.target.value)}
            size="small"
          />
        </div>

        <div>
          <div className={css`
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
          `}>
            列席联系人
          </div>
          <Input
            value={item.attendeeContact}
            placeholder="请输入列席联系人"
            onChange={(e) => onFieldChange(index, 'attendeeContact', e.target.value)}
            size="small"
          />
        </div>
      </div>

      <div className={css`
        margin-bottom: 16px;
      `}>
        <div className={css`
          font-size: 12px;
          color: #666;
          margin-bottom: 4px;
        `}>
          列席单位
        </div>
        <Input
          value={item.attendeeUnit}
          placeholder="请输入列席单位"
          onChange={(e) => onFieldChange(index, 'attendeeUnit', e.target.value)}
          size="small"
        />
      </div>

      <div className={css`
        display: flex;
        justify-content: flex-end;
        gap: 8px;
      `}>
        <Button size="small" onClick={() => onCancel(index)}>
          取消
        </Button>
        <Button type="primary" size="small" onClick={() => onSave(index)}>
          保存
        </Button>
      </div>
    </div>
  )
}



// 标记为Formily字段组件
AgendaArrayField.isFieldComponent = true

export default AgendaArrayField
